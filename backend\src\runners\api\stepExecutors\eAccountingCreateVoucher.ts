import axios from 'axios';
import type { EAccountingCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';
import { LLMService } from '../../../services/llmService';

/**
 * Executor context for eAccounting steps
 */
export interface EAccountingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * eAccounting voucher row interface
 */
interface EAccountingVoucherRow {
  AccountNumber: number;
  AccountDescription?: string;
  DebitAmount?: number;
  CreditAmount?: number;
  TransactionText?: string;
  CostCenterItemId1?: string;
  CostCenterItemId2?: string;
  CostCenterItemId3?: string;
  VatCodeId?: string;
  VatCodeAndPercent?: string;
  VatAmount?: number;
  Quantity?: number;
  Weight?: number;
  DeliveryDate?: string;
  HarvestYear?: number;
  ProjectId?: string;
}

/**
 * eAccounting voucher interface
 */
interface EAccountingVoucher {
  VoucherDate: string;
  VoucherText: string;
  Rows: EAccountingVoucherRow[];
  NumberSeries?: string;
  VoucherType?: number;
}

/**
 * AI response interface for voucher rows
 */
interface AIVoucherRowsResponse {
  rows: Array<{
    account: string;
    debit?: number;
    credit?: number;
    description?: string;
  }>;
  transactionDate?: string;
  explanation?: string;
}

/**
 * eAccounting voucher API response
 */
interface EAccountingVoucherResponse {
  Id?: string;
  VoucherDate?: string;
  VoucherText?: string;
  Rows?: EAccountingVoucherRow[];
  NumberAndNumberSeries?: string;
  NumberSeries?: string;
  ImportedVoucherNumber?: string;
  Attachments?: any;
  ModifiedUtc?: string;
  VoucherType?: number;
  SourceId?: string;
  CreatedUtc?: string;
}

/**
 * Execute eAccounting Create Voucher step
 */
export async function executeEAccountingCreateVoucher(
  step: EAccountingCreateVoucherStep,
  context: EAccountingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing eAccounting Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // Get eAccounting token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for eAccounting API calls');
    }

    const eAccountingTokens = await customerService.getCustomerTokensWithData(customerId);
    const eAccountingToken = eAccountingTokens.find(token => token.provider === 'eAccounting' && token.apiToken);

    if (!eAccountingToken || !eAccountingToken.apiToken) {
      throw new Error('No valid eAccounting token found for customer');
    }

    // Get input data from variable
    let actualVariableName = step.inputVariable;
    const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
    if (variableMatch) {
      actualVariableName = variableMatch[1];
    }

    const inputData = variables[actualVariableName];
    if (!inputData) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No input data found in variable: ${actualVariableName}`);
    }

    onLog({
      level: 'info',
      message: `Processing input data from variable: ${actualVariableName}`,
      stepId: step.id
    });

    // Get current LLM provider
    const provider = LLMService.getCurrentProvider();
    onLog({
      level: 'info',
      message: `Using ${provider.name} for AI processing`,
      stepId: step.id
    });

    // System prompt for AI to create voucher rows
    const systemPrompt = `Du är en expert på svensk bokföring och ska skapa verifikationsrader för eAccounting baserat på input-data.

VIKTIGT: Svara ENDAST med giltig JSON i följande format:
{
  "rows": [
    {
      "account": "kontonummer",
      "debit": belopp_eller_null,
      "credit": belopp_eller_null,
      "description": "beskrivning"
    }
  ],
  "transactionDate": "YYYY-MM-DD (om datum finns i input-data)",
  "explanation": "kort förklaring av verifikationen"
}

INSTRUKTIONER:
- Kontonummer ska vara numeriska (t.ex. "1930", "2440")
- Varje rad ska ha antingen debit ELLER credit, aldrig båda
- Verifikationen ska vara balanserad (totalt debit = totalt credit)
- Använd svenska beskrivningar
- Följ svensk kontoplan och bokföringssed
Användaren kommer att ange vilka konton som ska användas i sin prompt. Följ användarens instruktioner för kontohantering.`;

    // Interpolate the AI prompt with variables
    const interpolatedPrompt = interpolateVariables(step.aiPrompt, variables);

    // Prepare input data as string
    const inputDataStr = typeof inputData === 'string' ? inputData : JSON.stringify(inputData, null, 2);

    // Send to LLM for processing
    const completion = await LLMService.createChatCompletion([
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: `Skapa verifikationsrader för följande data:

INPUT DATA:
${inputDataStr}

INSTRUKTIONER:
${interpolatedPrompt}

Skapa en korrekt balanserad verifikation enligt svensk bokföringssed.`
      }
    ], {
      temperature: 0.1, // Lower temperature for more consistent output
      maxTokens: 2000
    });

    const aiResponse = completion.content;
    if (!aiResponse) {
      throw new Error('No response from LLM');
    }

    onLog({
      level: 'info',
      message: `AI processing completed, parsing voucher rows...`,
      stepId: step.id
    });

    // Parse AI response
    let aiVoucherData: AIVoucherRowsResponse;
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      aiVoucherData = JSON.parse(jsonStr);

      if (!aiVoucherData.rows || !Array.isArray(aiVoucherData.rows)) {
        throw new Error('AI response must contain a "rows" array');
      }

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown JSON parsing error';
      onLog({
        level: 'error',
        message: `Failed to parse AI response as JSON: ${errorMessage}. Raw response: ${aiResponse.substring(0, 200)}...`,
        stepId: step.id
      });
      throw new Error(`Invalid JSON response from AI: ${errorMessage}`);
    }

    // Validate and convert AI response to eAccounting format
    const voucherRows: EAccountingVoucherRow[] = [];
    let totalDebit = 0;
    let totalCredit = 0;

    for (const aiRow of aiVoucherData.rows) {
      // Validate amounts
      if (aiRow.debit && aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} cannot have both debit and credit`);
      }

      if (!aiRow.debit && !aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} must have either debit or credit`);
      }

      // Validate account number format (basic validation)
      if (!aiRow.account || !/^\d+$/.test(aiRow.account)) {
        throw new Error(`Invalid account number format: ${aiRow.account}. Must be numeric.`);
      }

      const voucherRow: EAccountingVoucherRow = {
        AccountNumber: parseInt(aiRow.account),
        TransactionText: aiRow.description || interpolateVariables(step.voucherText || '', variables)
      };

      if (aiRow.debit) {
        const debitAmount = parseFloat(aiRow.debit.toString());
        if (isNaN(debitAmount) || debitAmount <= 0) {
          throw new Error(`Invalid debit amount for account ${aiRow.account}: ${aiRow.debit}`);
        }
        voucherRow.DebitAmount = Math.round(debitAmount * 100) / 100; // Round to 2 decimals
        totalDebit += debitAmount;
      }

      if (aiRow.credit) {
        const creditAmount = parseFloat(aiRow.credit.toString());
        if (isNaN(creditAmount) || creditAmount <= 0) {
          throw new Error(`Invalid credit amount for account ${aiRow.account}: ${aiRow.credit}`);
        }
        voucherRow.CreditAmount = Math.round(creditAmount * 100) / 100; // Round to 2 decimals
        totalCredit += creditAmount;
      }

      voucherRows.push(voucherRow);

      onLog({
        level: 'info',
        message: `Added voucher row: Account ${aiRow.account}, Debit: ${aiRow.debit || 0}, Credit: ${aiRow.credit || 0}`,
        stepId: step.id
      });
    }

    // Validate that voucher is balanced
    const roundedTotalDebit = Math.round(totalDebit * 100) / 100;
    const roundedTotalCredit = Math.round(totalCredit * 100) / 100;

    if (Math.abs(roundedTotalDebit - roundedTotalCredit) > 0.01) {
      onLog({
        level: 'error',
        message: `Voucher is not balanced! Total debit: ${roundedTotalDebit}, Total credit: ${roundedTotalCredit}. Difference: ${Math.abs(roundedTotalDebit - roundedTotalCredit)}`,
        stepId: step.id
      });
      throw new Error(`Voucher is not balanced. Total debit: ${roundedTotalDebit}, Total credit: ${roundedTotalCredit}`);
    }

    onLog({
      level: 'info',
      message: `Voucher is balanced. Total amount: ${roundedTotalDebit}`,
      stepId: step.id
    });

    // Determine voucher date
    let voucherDate = step.voucherDate;
    if (!voucherDate && aiVoucherData.transactionDate) {
      voucherDate = aiVoucherData.transactionDate;
    }
    if (!voucherDate) {
      voucherDate = new Date().toISOString().split('T')[0]; // Today's date in YYYY-MM-DD format
    }
    voucherDate = interpolateVariables(voucherDate, variables);

    // Create voucher
    const voucher: EAccountingVoucher = {
      VoucherDate: voucherDate,
      VoucherText: interpolateVariables(step.voucherText || aiVoucherData.explanation || 'AI Generated Voucher', variables),
      Rows: voucherRows,
      NumberSeries: step.numberSeries,
      VoucherType: 2 // ManualVoucher
    };

    onLog({
      level: 'info',
      message: `Creating voucher with ${voucherRows.length} rows for date ${voucherDate}`,
      stepId: step.id
    });

    // Send voucher to eAccounting
    let voucherResponse;
    try {
      voucherResponse = await axios.post(
        'https://eaccountingapi.vismaonline.com/v2/vouchers',
        voucher,
        {
          headers: {
            'Authorization': `Bearer ${eAccountingToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `eAccounting Voucher API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`eAccounting Voucher API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdVoucher: EAccountingVoucherResponse = voucherResponse.data;

    onLog({
      level: 'info',
      message: `Voucher created successfully. ID: ${createdVoucher.Id}, Number: ${createdVoucher.NumberAndNumberSeries}`,
      stepId: step.id
    });

    // Store result in variables
    const variableName = step.variableName || getDefaultVariableName('eAccountingCreateVoucher', stepIndex);
    const result = {
      voucherId: createdVoucher.Id,
      voucherNumber: createdVoucher.NumberAndNumberSeries,
      voucherSeries: createdVoucher.NumberSeries,
      voucherDate: createdVoucher.VoucherDate,
      voucherText: createdVoucher.VoucherText,
      totalAmount: roundedTotalDebit,
      rowsCount: voucherRows.length,
      aiExplanation: aiVoucherData.explanation || "Verifikation skapad med AI-assistans",
      createdUtc: createdVoucher.CreatedUtc
    };

    return {
      success: true,
      variables: {
        [variableName]: result
      }
    };

  } catch (error) {
    onLog({
      level: 'error',
      message: `eAccounting Create Voucher failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stepId: step.id
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during voucher creation'
    };
  }
}
