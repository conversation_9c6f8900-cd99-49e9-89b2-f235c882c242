import axios from 'axios';
import type { EAccountingUploadFileStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for eAccounting steps
 */
export interface EAccountingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * eAccounting Attachment API response interface
 */
interface EAccountingAttachmentResponse {
  Id?: string;
  ContentType?: string;
  DocumentId?: string;
  AttachedDocumentType?: number;
  MessageThreads?: string[];
  Notes?: string[];
  PhotoSource?: number;
  FileName?: string;
  TemporaryUrl?: string;
  Comment?: string;
  SupplierName?: string;
  CurrencyCode?: string;
  AmountInvoiceCurrency?: number;
  Type?: number;
  AttachmentStatus?: number;
  UploadedBy?: string;
  ImageDate?: string;
  TransactionDate?: string;
}

/**
 * Execute eAccounting Upload File step
 */
export async function executeEAccountingUploadFile(
  step: EAccountingUploadFileStep,
  context: EAccountingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing eAccounting Upload File: ${step.id}`,
      stepId: step.id
    });

    // Get eAccounting token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for eAccounting API calls');
    }

    const eAccountingTokens = await customerService.getCustomerTokensWithData(customerId);
    const eAccountingToken = eAccountingTokens.find(token => token.provider === 'eAccounting' && token.apiToken);

    if (!eAccountingToken || !eAccountingToken.apiToken) {
      throw new Error('No valid eAccounting token found for customer');
    }

    // Get base64 file content from variable
    // Handle both direct variable names and ${variableName} syntax
    let actualVariableName = step.inputVariable;

    // Check if inputVariable contains ${...} syntax and extract the variable name
    const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
    if (variableMatch) {
      actualVariableName = variableMatch[1];
    }

    const base64Content = variables[actualVariableName];

    if (!base64Content) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No file content found in variable: ${actualVariableName}`);
    }

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = base64Content;
    if (typeof base64Data === 'string' && base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Validate base64 data
    try {
      Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data in variable ${actualVariableName}: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    onLog({
      level: 'info',
      message: `Base64 data validated, length: ${base64Data.length} characters`,
      stepId: step.id
    });

    // Determine filename
    let filename: string = step.filename || '';
    if (!filename) {
      // Try to get filename from related variable
      const filenameVariable = `${actualVariableName}_filename`;
      filename = variables[filenameVariable] || 'uploaded_file.pdf';
    }
    filename = interpolateVariables(filename, variables);

    // Determine content type
    let contentType = step.contentType || '';
    if (!contentType) {
      // Auto-detect content type from filename extension
      const extension = filename.toLowerCase().split('.').pop();
      switch (extension) {
        case 'pdf':
          contentType = 'application/pdf';
          break;
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'tiff':
        case 'tif':
          contentType = 'image/tiff';
          break;
        default:
          contentType = 'application/pdf'; // Default to PDF
      }
    }

    onLog({
      level: 'info',
      message: `Uploading file: ${filename} (${contentType})`,
      stepId: step.id
    });

    // Prepare attachment upload data according to eAccounting API
    const attachmentData = {
      ContentType: contentType,
      FileName: filename,
      Data: base64Data,
      Comment: step.comment ? interpolateVariables(step.comment, variables) : undefined
    };

    // Upload file to eAccounting Attachments
    let uploadResponse;
    try {
      uploadResponse = await axios.post(
        'https://eaccountingapi.vismaonline.com/v2/attachments',
        attachmentData,
        {
          headers: {
            'Authorization': `Bearer ${eAccountingToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `eAccounting Attachments API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`eAccounting Attachments API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const uploadedAttachment: EAccountingAttachmentResponse = uploadResponse.data;

    onLog({
      level: 'info',
      message: `File uploaded successfully. Attachment ID: ${uploadedAttachment.Id}`,
      stepId: step.id
    });

    // Store result in variables
    const variableName = step.variableName || getDefaultVariableName('eAccountingUploadFile', stepIndex);
    const result = {
      attachmentId: uploadedAttachment.Id,
      filename: uploadedAttachment.FileName || filename,
      contentType: uploadedAttachment.ContentType || contentType,
      comment: uploadedAttachment.Comment,
      temporaryUrl: uploadedAttachment.TemporaryUrl,
      uploadedBy: uploadedAttachment.UploadedBy,
      imageDate: uploadedAttachment.ImageDate,
      transactionDate: uploadedAttachment.TransactionDate,
      attachmentStatus: uploadedAttachment.AttachmentStatus,
      type: uploadedAttachment.Type
    };

    return {
      success: true,
      variables: {
        [variableName]: result
      }
    };

  } catch (error) {
    onLog({
      level: 'error',
      message: `eAccounting Upload File failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stepId: step.id
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during file upload'
    };
  }
}
