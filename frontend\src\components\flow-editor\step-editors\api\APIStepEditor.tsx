import { useState } from 'react'
import { validateStep, RpaStep } from '@rpa-project/shared'
import type {
  FortnoxCreateVoucherStep,
  FortnoxUploadFileStep,
  FortnoxAttachFileToVoucherStep,
  FortnoxUploadAndCreateVoucherStep,
  EAccountingUploadFileStep,
  EAccountingCreateVoucherStep,
  EAccountingAttachFileToVoucherStep,
  EAccountingUploadAndCreateVoucherStep
} from '@rpa-project/shared/dist/esm/types/steps/api'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { TextAreaField, VariableField, SelectField } from '../base/FieldComponents'

type APIStep = FortnoxCreateVoucherStep | FortnoxUploadFileStep | FortnoxAttachFileToVoucherStep | FortnoxUploadAndCreateVoucherStep

interface APIStepEditorProps extends BaseStepEditorProps {
  step: RpaStep
}

export function APIStepEditor({
  step,
  onSave,
  onCancel,
  compact = false,
  steps = [],
  currentStepIndex = 0
}: APIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<APIStep>({ ...step } as unknown as APIStep)
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep as unknown as RpaStep)
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep as unknown as RpaStep)
  }

  const updateStep = (updates: Partial<APIStep>) => {
    setEditedStep((prev: APIStep) => ({ ...prev, ...updates } as APIStep))
  }

  const renderFortnoxCreateVoucherFields = () => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep

    return (
      <>
        <TextAreaField
          label="Beskrivning"
          value={fortnoxStep.description || ''}
          onChange={(value) => updateStep({ description: value })}
          placeholder="Beskrivning av verifikationen"
          compact={compact}
        />

        <VariableField
          label="Input-variabel"
          value={fortnoxStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med data för verifikationen"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <TextAreaField
          label="AI-prompt"
          value={fortnoxStep.aiPrompt || ''}
          onChange={(value) => updateStep({ aiPrompt: value })}
          placeholder="Beskriv hur AI:n ska skapa verifikationsraderna, t.ex. 'Skapa en inköpsverifikation med moms'"
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr 1fr', gap: '1rem' }}>
          <SelectField
            label="Verifikationsserie"
            value={fortnoxStep.voucherSeries || 'A'}
            onChange={(value) => updateStep({ voucherSeries: value })}
            options={[
              { value: 'A', label: 'A - Allmän' },
              { value: 'B', label: 'B - Bank' },
              { value: 'C', label: 'C - Kassa' },
              { value: 'D', label: 'D - Dagsrapporter' },
              { value: 'Z', label: 'Z - Övrigt' }
            ]}
            compact={compact}
          />

          <VariableField
            label="Transaktionsdatum"
            value={fortnoxStep.transactionDate || ''}
            onChange={(value) => updateStep({ transactionDate: value })}
            placeholder="YYYY-MM-DD eller välj variabel"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={fortnoxStep.variableName || 'var-fortnox-voucher'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för verifikationsresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '0.5rem',
          border: '1px solid #0ea5e9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>🤖</span>
            <span style={{ fontWeight: '500', color: '#0369a1' }}>AI-driven verifikationsskapande</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#0369a1',
            lineHeight: '1.4'
          }}>
            AI:n kommer att analysera input-data och skapa balanserade verifikationsrader automatiskt baserat på din prompt och Fortnox kontoplan.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxUploadFileFields = () => {
    const uploadStep = editedStep as FortnoxUploadFileStep

    return (
      <>
        <VariableField
          label="Fil-variabel"
          value={uploadStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med base64-filinnehåll"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr', gap: '1rem' }}>
          <VariableField
            label="Filnamn (valfritt)"
            value={uploadStep.filename || ''}
            onChange={(value) => updateStep({ filename: value })}
            placeholder="Anpassat filnamn eller lämna tomt för automatiskt"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={uploadStep.variableName || 'var-fortnox-file'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för filresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <TextAreaField
          label="Beskrivning (valfritt)"
          value={uploadStep.description || ''}
          onChange={(value) => updateStep({ description: value })}
          placeholder="Beskrivning av filen"
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0fdf4',
          borderRadius: '0.5rem',
          border: '1px solid #22c55e'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📤</span>
            <span style={{ fontWeight: '500', color: '#15803d' }}>Filuppladdning till Fortnox</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#15803d',
            lineHeight: '1.4'
          }}>
            Filen kommer att laddas upp till Fortnox arkiv och returnera ett fil-ID som kan användas för att koppla till verifikationer.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxAttachFileToVoucherFields = () => {
    const attachStep = editedStep as FortnoxAttachFileToVoucherStep

    return (
      <>
        <VariableField
          label="Fil-ID variabel"
          value={attachStep.fileIdVariable || ''}
          onChange={(value) => updateStep({ fileIdVariable: value })}
          placeholder="Välj variabel med fil-ID från uppladdning"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr 1fr', gap: '1rem' }}>
          <VariableField
            label="Verifikationsnummer-variabel"
            value={attachStep.voucherNumberVariable || ''}
            onChange={(value) => updateStep({ voucherNumberVariable: value })}
            placeholder="Välj variabel med verifikationsnummer"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Verifikationsserie-variabel (valfritt)"
            value={attachStep.voucherSeriesVariable || ''}
            onChange={(value) => updateStep({ voucherSeriesVariable: value })}
            placeholder="Välj variabel med verifikationsserie"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={attachStep.variableName || 'var-fortnox-attachment'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för kopplingsresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '0.5rem',
          border: '1px solid #f59e0b'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📎</span>
            <span style={{ fontWeight: '500', color: '#92400e' }}>Filkoppling till verifikation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#92400e',
            lineHeight: '1.4'
          }}>
            Kopplar en redan uppladdad fil till en befintlig verifikation i Fortnox.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxUploadAndCreateVoucherFields = () => {
    const combinedStep = editedStep as FortnoxUploadAndCreateVoucherStep

    return (
      <>
        {/* Two-column layout with full width */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '2rem',
          marginBottom: '1.5rem',
          width: '100%'
        }}>
          {/* Left column: File Upload and Output */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              📤 Filuppladdning
            </h4>

            <VariableField
              label="Fil-variabel"
              value={combinedStep.fileInputVariable || ''}
              onChange={(value) => updateStep({ fileInputVariable: value })}
              placeholder="Välj variabel med base64-filinnehåll"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <h4 style={{
              margin: '2rem 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              📦 Output
            </h4>

            <VariableField
              label="Resultat-variabel"
              value={combinedStep.variableName || 'var-fortnox-voucher-with-file'}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="Variabelnamn för kombinerat resultat"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
          </div>

          {/* Right column: Voucher Creation */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              🧾 Verifikationsskapande
            </h4>

            <VariableField
              label="Verifikation input-variabel"
              value={combinedStep.voucherInputVariable || ''}
              onChange={(value) => updateStep({ voucherInputVariable: value })}
              placeholder="Välj variabel med data för verifikationen"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <div style={{ marginTop: '1rem' }}>
              <TextAreaField
                label="AI-prompt"
                value={combinedStep.aiPrompt || ''}
                onChange={(value) => updateStep({ aiPrompt: value })}
                placeholder="Beskriv hur AI:n ska skapa verifikationsraderna"
                compact={compact}
                minHeight='150px'
              />
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: compact ? '1fr' : '1fr 1fr',
              gap: '1rem',
              marginTop: '1rem'
            }}>
              <TextAreaField
                label="Verifikationsbeskrivning (valfritt)"
                value={combinedStep.voucherDescription || ''}
                onChange={(value) => updateStep({ voucherDescription: value })}
                placeholder="Beskrivning av verifikationen"
                compact={compact}
              />

              <SelectField
                label="Verifikationsserie"
                value={combinedStep.voucherSeries || 'A'}
                onChange={(value) => updateStep({ voucherSeries: value })}
                options={[
                  { value: 'A', label: 'A - Allmän' },
                  { value: 'B', label: 'B - Bank' },
                  { value: 'C', label: 'C - Kassa' },
                  { value: 'D', label: 'D - Dagsrapporter' },
                  { value: 'Z', label: 'Z - Övrigt' }
                ]}
                compact={compact}
              />
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '0.5rem',
          border: '1px solid #0ea5e9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📋</span>
            <span style={{ fontWeight: '500', color: '#0c4a6e' }}>Kombinerad operation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#0c4a6e',
            lineHeight: '1.4'
          }}>
            Laddar upp filen till Fortnox arkiv, skapar en verifikation med AI och kopplar automatiskt filen till verifikationen.
          </p>
        </div>
      </>
    )
  }

  const renderAPIFields = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return renderFortnoxCreateVoucherFields()
      case 'fortnoxUploadFile':
        return renderFortnoxUploadFileFields()
      case 'fortnoxAttachFileToVoucher':
        return renderFortnoxAttachFileToVoucherFields()
      case 'fortnoxUploadAndCreateVoucher':
        return renderFortnoxUploadAndCreateVoucherFields()
      default:
        return (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f9fafb',
            borderRadius: '0.5rem',
            color: '#6b7280',
            fontSize: compact ? '0.875rem' : '1rem'
          }}>
            <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>🚧</div>
            <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>API Steps Coming Soon</div>
            <div style={{ fontSize: '0.875rem' }}>
              API step editors will be implemented in future versions.
            </div>
          </div>
        )
    }
  }

  const getTitle = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return compact ? 'Skapa Fortnox Verifikation' : 'Konfigurera Fortnox Verifikation'
      case 'fortnoxUploadFile':
        return compact ? 'Ladda upp fil till Fortnox' : 'Konfigurera Fortnox Filuppladdning'
      case 'fortnoxAttachFileToVoucher':
        return compact ? 'Koppla fil till verifikation' : 'Konfigurera Filkoppling'
      case 'fortnoxUploadAndCreateVoucher':
        return compact ? 'Ladda upp fil och skapa verifikation' : 'Konfigurera Kombinerad Operation'
      default:
        return compact ? 'Konfigurera API-steg' : 'Edit API Step'
    }
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAPIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
