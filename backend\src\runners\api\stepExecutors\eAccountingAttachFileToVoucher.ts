import axios from 'axios';
import type { EAccountingAttachFileToVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for eAccounting steps
 */
export interface EAccountingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * eAccounting Attachment Link API request interface
 */
interface EAccountingAttachmentLinkRequest {
  DocumentId: string;
  DocumentType: number; // 3 = Voucher
  AttachmentIds: string[];
}

/**
 * eAccounting Attachment Link API response interface
 */
interface EAccountingAttachmentLinkResponse {
  DocumentId?: string;
  DocumentType?: number;
  AttachmentIds?: string[];
}

/**
 * Execute eAccounting Attach File to Voucher step
 */
export async function executeEAccountingAttachFileToVoucher(
  step: EAccountingAttachFileToVoucherStep,
  context: EAccountingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing eAccounting Attach File to Voucher: ${step.id}`,
      stepId: step.id
    });

    // Get eAccounting token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for eAccounting API calls');
    }

    const eAccountingTokens = await customerService.getCustomerTokensWithData(customerId);
    const eAccountingToken = eAccountingTokens.find(token => token.provider === 'eAccounting' && token.apiToken);

    if (!eAccountingToken || !eAccountingToken.apiToken) {
      throw new Error('No valid eAccounting token found for customer');
    }

    // Get attachment ID from variable
    let actualAttachmentVariableName = step.attachmentIdVariable;
    const attachmentVariableMatch = step.attachmentIdVariable.match(/^\$\{([^}]+)\}$/);
    if (attachmentVariableMatch) {
      actualAttachmentVariableName = attachmentVariableMatch[1];
    }

    const attachmentData = variables[actualAttachmentVariableName];
    if (!attachmentData) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No attachment data found in variable: ${actualAttachmentVariableName}`);
    }

    // Extract attachment ID from the attachment data
    let attachmentId: string;
    if (typeof attachmentData === 'string') {
      attachmentId = attachmentData;
    } else if (attachmentData && typeof attachmentData === 'object' && attachmentData.attachmentId) {
      attachmentId = attachmentData.attachmentId;
    } else {
      throw new Error(`Invalid attachment data format in variable ${actualAttachmentVariableName}. Expected string or object with attachmentId property.`);
    }

    onLog({
      level: 'info',
      message: `Using attachment ID: ${attachmentId}`,
      stepId: step.id
    });

    // Get voucher ID from variable
    let actualVoucherVariableName = step.voucherIdVariable;
    const voucherVariableMatch = step.voucherIdVariable.match(/^\$\{([^}]+)\}$/);
    if (voucherVariableMatch) {
      actualVoucherVariableName = voucherVariableMatch[1];
    }

    const voucherData = variables[actualVoucherVariableName];
    if (!voucherData) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No voucher data found in variable: ${actualVoucherVariableName}`);
    }

    // Extract voucher ID from the voucher data
    let voucherId: string;
    if (typeof voucherData === 'string') {
      voucherId = voucherData;
    } else if (voucherData && typeof voucherData === 'object' && voucherData.voucherId) {
      voucherId = voucherData.voucherId;
    } else {
      throw new Error(`Invalid voucher data format in variable ${actualVoucherVariableName}. Expected string or object with voucherId property.`);
    }

    onLog({
      level: 'info',
      message: `Attaching file ${attachmentId} to voucher ${voucherId}`,
      stepId: step.id
    });

    // Prepare attachment link request
    const attachmentLinkRequest: EAccountingAttachmentLinkRequest = {
      DocumentId: voucherId,
      DocumentType: 3, // 3 = Voucher according to API documentation
      AttachmentIds: [attachmentId]
    };

    // Send attachment link request to eAccounting
    let attachmentResponse;
    try {
      attachmentResponse = await axios.post(
        'https://eaccountingapi.vismaonline.com/v2/attachmentlinks',
        attachmentLinkRequest,
        {
          headers: {
            'Authorization': `Bearer ${eAccountingToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from eAccounting API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `eAccounting Attachment Links API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`eAccounting Attachment Links API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const attachmentLinkResult: EAccountingAttachmentLinkResponse = attachmentResponse.data;

    onLog({
      level: 'info',
      message: `File attached successfully to voucher. Document ID: ${attachmentLinkResult.DocumentId}, Attachment IDs: ${attachmentLinkResult.AttachmentIds?.join(', ')}`,
      stepId: step.id
    });

    // Store result in variables
    const variableName = step.variableName || getDefaultVariableName('eAccountingAttachFileToVoucher', stepIndex);
    const result = {
      documentId: attachmentLinkResult.DocumentId || voucherId,
      documentType: attachmentLinkResult.DocumentType || 3,
      attachmentIds: attachmentLinkResult.AttachmentIds || [attachmentId],
      attachmentId: attachmentId,
      voucherId: voucherId,
      success: true,
      message: `File ${attachmentId} successfully attached to voucher ${voucherId}`
    };

    return {
      success: true,
      variables: {
        [variableName]: result
      }
    };

  } catch (error) {
    onLog({
      level: 'error',
      message: `eAccounting Attach File to Voucher failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stepId: step.id
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during file attachment'
    };
  }
}
